import React, { useState, useRef, useCallback, useEffect } from 'react';
import { useNavigate } from '@tanstack/react-router';
import { useQueryClient } from '@tanstack/react-query';
import { DataTableStateEvent } from 'primereact/datatable';
import { TabView, TabPanel } from 'primereact/tabview';
import { Dropdown } from 'primereact/dropdown';
import { Paginator } from 'primereact/paginator';
import { ColumnConfig, DataGrid } from '@/components/ui/DataGrid/DataGrid';
import Card from '@/components/ui/Card/Card';
import Button from '@/components/ui/Button/Button';
import Modal from '@/components/ui/Modal/Modal';
import Toast, { ToastRef } from '@/components/ui/Toast/Toast';
import {
  useProducts,
  useDeleteProduct,
  useCategories,
  useDeleteCategory,
  useSubcategories,
  useSubcategoriesByCategory,
  useDeleteSubcategory
} from '@/hooks/useCatalog';
import { Product, Category, Subcategory } from '@/types/catalog.types';
import { QueryParams } from '@/types/api/common';
import { editProductRoute } from '@/routes/private/editProduct.route';
import { addProductRoute } from '@/routes/private/addProduct.route';
import { addCategoryRoute } from '@/routes/private/addCategory.route';
import { editCategoryRoute } from '@/routes/private/editCategory.route';
import { addSubcategoryRoute } from '@/routes/private/addSubcategory.route';
import { editSubcategoryRoute } from '@/routes/private/editSubcategory.route';
import './OrganizationItemCatalog.css';

type DeleteItemType = 'category' | 'subcategory' | 'product';

interface DropdownOption {
  label: string;
  value: number | null;
  disabled?: boolean;
}

const OrganizationItemCatalog: React.FC = () => {
  const navigate = useNavigate();
  const toast = useRef<ToastRef>(null);
  const queryClient = useQueryClient();

  // State for active tab
  const [activeTabIndex, setActiveTabIndex] = useState<number>(0);

  // State for category and subcategory filters
  const [selectedCategoryId, setSelectedCategoryId] = useState<number | null>(null);
  const [selectedSubcategoryId, setSelectedSubcategoryId] = useState<number | null>(null);

  // State for pagination and sorting - Categories
  const [categoryPageSize, setCategoryPageSize] = useState<number>(10);
  const [categoryCurrentPage, setCategoryCurrentPage] = useState<number>(1);
  const [categorySortField, setCategorySortField] = useState<string>('name');
  const [categorySortOrder, setCategorySortOrder] = useState<'asc' | 'desc'>('asc');

  // State for pagination and sorting - Subcategories
  const [subcategoryPageSize, setSubcategoryPageSize] = useState<number>(10);
  const [subcategoryCurrentPage, setSubcategoryCurrentPage] = useState<number>(1);
  const [subcategorySortField, setSubcategorySortField] = useState<string>('name');
  const [subcategorySortOrder, setSubcategorySortOrder] = useState<'asc' | 'desc'>('asc');

  // State for pagination and sorting - Products
  const [productPageSize, setProductPageSize] = useState<number>(12);
  const [productCurrentPage, setProductCurrentPage] = useState<number>(1);
  const [productSortField, setProductSortField] = useState<string>('name');
  const [productSortOrder, setProductSortOrder] = useState<'asc' | 'desc'>('asc');

  // State for delete confirmation
  const [deleteModalVisible, setDeleteModalVisible] = useState<boolean>(false);
  const [itemToDelete, setItemToDelete] = useState<Category | Subcategory | Product | null>(null);
  const [deleteItemType, setDeleteItemType] = useState<DeleteItemType>('product');

  // Query params for API requests
  const categoryQueryParams: QueryParams = {
    page: categoryCurrentPage,
    limit: categoryPageSize,
    sort: `${categorySortField},${categorySortOrder}`,
  };

  const subcategoryQueryParams: QueryParams = {
    page: subcategoryCurrentPage,
    limit: subcategoryPageSize,
    sort: `${subcategorySortField},${subcategorySortOrder}`,
    // Only add category filter if a specific category is selected (not "All Categories")
    ...(selectedCategoryId !== null && { filter: { category_id: selectedCategoryId } }),
  };

  const productFilter: Record<string, any> = {};
  if (selectedCategoryId !== null) { // null check is sufficient
    productFilter.category_id = selectedCategoryId;
  }
  if (selectedSubcategoryId !== null) { // null check is sufficient
    productFilter.subcategory_id = selectedSubcategoryId;
  }

  const productQueryParams: QueryParams = {
    page: productCurrentPage,
    limit: productPageSize,
    sort: `${productSortField},${productSortOrder}`,
    ...(Object.keys(productFilter).length > 0 && { filter: productFilter }),
  };

  // Fetch categories data
  const {
    data: categoriesData,
    isLoading: isCategoriesLoading,
    refetch: refetchCategories
  } = useCategories(categoryQueryParams);
  const categories = categoriesData?.data || [];
  const categoriesTotal = categoriesData?.total || 0;

  // Fetch subcategories data
  const {
    data: subcategoriesData,
    isLoading: isSubcategoriesLoading,
    refetch: refetchSubcategories
  } = useSubcategories(subcategoryQueryParams);
  const subcategories = subcategoriesData?.data || [];
  const subcategoriesTotal = subcategoriesData?.total || 0;

  // Fetch products data
  const {
    data: productsData,
    isLoading: isProductsLoading,
    refetch: refetchProducts
  } = useProducts(productQueryParams);
  const products = productsData?.data || [];
  const productsTotal = productsData?.total || 0;

  // Debug logging will be moved after filteredSubcategories is defined

  // Delete mutations
  const deleteProductMutation = useDeleteProduct();
  const deleteCategoryMutation = useDeleteCategory();
  const deleteSubcategoryMutation = useDeleteSubcategory();

  useEffect(() => {
    if (selectedCategoryId === null) {
      // If "All Categories" is selected, the subcategory filter can still apply globally.
      // However, we need to ensure that if a subcategory is selected, it exists in the current data
      if (selectedSubcategoryId !== null) {
        const selectedSubcategoryExists = subcategories.some(
          (sub) => sub.id === selectedSubcategoryId
        );
        if (!selectedSubcategoryExists) {
          console.log('Selected subcategory not found in current data, resetting to null');
          setSelectedSubcategoryId(null);
        }
      }
      return;
    }

    if (selectedSubcategoryId !== null) {
      // Check if the currently selected subcategory belongs to the selected category.
      // This relies on `subcategories` data being up-to-date.
      const selectedSubcategoryIsValid = subcategories.some(
        (sub) => sub.id === selectedSubcategoryId && sub.categoryId === selectedCategoryId
      );
      if (!selectedSubcategoryIsValid) {
        console.log('Selected subcategory not valid for current category, resetting to null');
        setSelectedSubcategoryId(null); // Reset if not valid
      }
    }
  }, [selectedCategoryId, selectedSubcategoryId, subcategories, setSelectedSubcategoryId]);


  // Handle tab change
  const handleTabChange = useCallback((e: { index: number }) => {
    setActiveTabIndex(e.index);
  }, []);

  // Category tab handlers
  const handleCategoryPageChange = useCallback((event: { first: number; rows: number; page: number }) => {
    setCategoryCurrentPage(event.page + 1);
  }, []);

  const handleCategorySort = useCallback((event: DataTableStateEvent) => {
    const field = event.sortField || 'name';
    const order = event.sortOrder === 1 ? 'asc' : 'desc';

    setCategorySortField(field);
    setCategorySortOrder(order);
  }, []);

  const handleAddCategory = useCallback(() => {
    navigate({ to: addCategoryRoute.to });
  }, [navigate]);

  const handleEditCategory = useCallback((category: Category) => {
    navigate({
      to: editCategoryRoute.to,
      search: { id: category.id?.toString() }
    });
  }, [navigate]);

  const handleDeleteCategory = useCallback((category: Category) => {
    setItemToDelete(category);
    setDeleteItemType('category');
    setDeleteModalVisible(true);
  }, []);

  // Subcategory tab handlers
  const handleSubcategoryPageChange = useCallback((event: { first: number; rows: number; page: number }) => {
    setSubcategoryCurrentPage(event.page + 1);
  }, []);

  const handleSubcategorySort = useCallback((event: DataTableStateEvent) => {
    const field = event.sortField || 'name';
    const order = event.sortOrder === 1 ? 'asc' : 'desc';

    setSubcategorySortField(field);
    setSubcategorySortOrder(order);
  }, []);

  const handleCategoryFilterChange = useCallback((e: { value: number | null }) => {
    console.log('Subcategory tab - Category filter changed to:', e.value);
    setSelectedCategoryId(e.value);
    setSubcategoryCurrentPage(1);
    // Reset subcategory selection when changing category filter
    setSelectedSubcategoryId(null);
    // Force refetch of subcategories when category filter changes
    setTimeout(() => refetchSubcategories(), 0);
  }, [refetchSubcategories]);

  const handleAddSubcategory = useCallback(() => {
    navigate({
      to: addSubcategoryRoute.to,
      search: selectedCategoryId ? { categoryId: selectedCategoryId.toString() } : undefined
    });
  }, [navigate, selectedCategoryId]);

  const handleEditSubcategory = useCallback((subcategory: Subcategory) => {
    navigate({
      to: editSubcategoryRoute.to,
      search: { id: subcategory.id?.toString() }
    });
  }, [navigate]);

  const handleDeleteSubcategory = useCallback((subcategory: Subcategory) => {
    setItemToDelete(subcategory);
    setDeleteItemType('subcategory');
    setDeleteModalVisible(true);
  }, []);

  // Product tab handlers
  const handleProductPageChange = useCallback((event: { first: number; rows: number; page: number }) => {
    setProductCurrentPage(event.page + 1);
  }, []);

  const handleProductSort = useCallback((event: DataTableStateEvent) => {
    const field = event.sortField || 'name';
    const order = event.sortOrder === 1 ? 'asc' : 'desc';

    setProductSortField(field);
    setProductSortOrder(order);
  }, []);

  const handleProductCategoryFilterChange = useCallback((e: { value: number | null }) => {
    console.log('Product category filter changed to:', e.value);
    setSelectedCategoryId(e.value);
    // When the main category filter changes, always reset the subcategory filter.
    // This provides a cleaner state: selecting "All Categories" (null) will clear both filters for products.
    setSelectedSubcategoryId(null);
    setProductCurrentPage(1);
    // Force refetch of subcategories to ensure we have the correct data for the new filter state
    setTimeout(() => refetchSubcategories(), 0);
    // Invalidate products query to ensure fresh data with new filter state
    queryClient.invalidateQueries({ queryKey: ['products'] });
  }, [
    refetchSubcategories,
    setSelectedCategoryId,
    setSelectedSubcategoryId,
    setProductCurrentPage,
    queryClient
  ]);

  const handleProductSubcategoryFilterChange = useCallback((e: { value: number | null }) => {
    console.log('Product subcategory filter changed to:', e.value);
    setSelectedSubcategoryId(e.value);
    setProductCurrentPage(1);
    // Invalidate products query to ensure fresh data with new filter state
    queryClient.invalidateQueries({ queryKey: ['products'] });
  }, [setSelectedSubcategoryId, setProductCurrentPage, queryClient]);


  const handleAddProduct = useCallback(() => {
    navigate({
      to: addProductRoute.to,
      search: {
        ...(selectedCategoryId !== null ? { categoryId: selectedCategoryId.toString() } : {}),
        ...(selectedSubcategoryId !== null ? { subcategoryId: selectedSubcategoryId.toString() } : {})
      }
    });
  }, [navigate, selectedCategoryId, selectedSubcategoryId]);

  const handleEditProduct = useCallback((product: Product) => {
    navigate({
      to: editProductRoute.to,
      search: { id: product.id?.toString() }
    });
  }, [navigate]);

  const handleDeleteProduct = useCallback((product: Product) => {
    setItemToDelete(product);
    setDeleteItemType('product');
    setDeleteModalVisible(true);
  }, []);

  // Confirm delete item
  const confirmDelete = useCallback(async () => {
    if (!itemToDelete || !itemToDelete.id) return;

    try {
      if (deleteItemType === 'category') {
        await deleteCategoryMutation.mutateAsync(itemToDelete.id);
        toast.current?.showSuccess('Category deleted successfully');
        refetchCategories();
      } else if (deleteItemType === 'subcategory') {
        const subcategory = itemToDelete as Subcategory;
        if (subcategory.categoryId && subcategory.id) {
          await deleteSubcategoryMutation.mutateAsync({
            categoryId: subcategory.categoryId,
            id: subcategory.id
          });
          toast.current?.showSuccess('Subcategory deleted successfully');
          refetchSubcategories();
        }
      } else {
        await deleteProductMutation.mutateAsync(itemToDelete.id);
        toast.current?.showSuccess('Product deleted successfully');
        refetchProducts();
      }
    } catch (error) {
      console.error(`Error deleting ${deleteItemType}:`, error);
      toast.current?.showError(`Failed to delete ${deleteItemType}`);
    } finally {
      setDeleteModalVisible(false);
      setItemToDelete(null);
    }
  }, [
    itemToDelete,
    deleteItemType,
    deleteCategoryMutation,
    deleteSubcategoryMutation,
    deleteProductMutation,
    refetchCategories,
    refetchSubcategories,
    refetchProducts
  ]);

  // Cancel delete
  const cancelDelete = useCallback(() => {
    setDeleteModalVisible(false);
    setItemToDelete(null);
  }, []);

  // Image cell template for categories
  const categoryImageBodyTemplate = useCallback((category: Category) => {
    return (
      <div className="image-cell">
        {category.imagePath ? (
          <img src={category.imagePath} alt={category.name} className="thumbnail-image" />
        ) : (
          <div className="image-placeholder">
            <i className="pi pi-image"></i>
          </div>
        )}
      </div>
    );
  }, []);

  // Image cell template for subcategories
  const subcategoryImageBodyTemplate = useCallback((subcategory: Subcategory) => {
    return (
      <div className="image-cell">
        {subcategory.imagePath ? (
          <img src={subcategory.imagePath} alt={subcategory.name} className="thumbnail-image" />
        ) : (
          <div className="image-placeholder">
            <i className="pi pi-image"></i>
          </div>
        )}
      </div>
    );
  }, []);

  // Image cell template for products
  const productImageBodyTemplate = useCallback((product: Product) => {
    return (
      <div className="image-cell">
        {product.imagePaths && product.imagePaths.length > 0 ? (
          <img src={product.imagePaths[0]} alt={product.name} className="thumbnail-image" />
        ) : (
          <div className="image-placeholder">
            <i className="pi pi-image"></i>
          </div>
        )}
      </div>
    );
  }, []);

  // Actions cell template for categories
  const categoryActionsBodyTemplate = useCallback((category: Category) => {
    return (
      <div className="action-buttons">
        <Button
          variant="outline"
          size="small"
          leftIcon={<i className="pi pi-pencil"></i>}
          onClick={() => handleEditCategory(category)}
        />
        <Button
          variant="danger"
          size="small"
          leftIcon={<i className="pi pi-trash"></i>}
          onClick={() => handleDeleteCategory(category)}
        />
      </div>
    );
  }, [handleEditCategory, handleDeleteCategory]);

  // Actions cell template for subcategories
  const subcategoryActionsBodyTemplate = useCallback((subcategory: Subcategory) => {
    return (
      <div className="action-buttons">
        <Button
          variant="outline"
          size="small"
          leftIcon={<i className="pi pi-pencil"></i>}
          onClick={() => handleEditSubcategory(subcategory)}
        />
        <Button
          variant="danger"
          size="small"
          leftIcon={<i className="pi pi-trash"></i>}
          onClick={() => handleDeleteSubcategory(subcategory)}
        />
      </div>
    );
  }, [handleEditSubcategory, handleDeleteSubcategory]);

  // Actions cell template for products
  const productActionsBodyTemplate = useCallback((product: Product) => {
    return (
      <div className="action-buttons">
        <Button
          variant="outline"
          size="small"
          leftIcon={<i className="pi pi-pencil"></i>}
          onClick={() => handleEditProduct(product)}
        />
        <Button
          variant="danger"
          size="small"
          leftIcon={<i className="pi pi-trash"></i>}
          onClick={() => handleDeleteProduct(product)}
        />
      </div>
    );
  }, [handleEditProduct, handleDeleteProduct]);

  // Category DataGrid columns configuration
  const categoryColumns: ColumnConfig[] = [
    {
      field: 'imagePath',
      header: 'Image',
      body: categoryImageBodyTemplate,
      style: { width: '80px' },
    },
    {
      field: 'name',
      header: 'Name',
      sortable: true,
    },
    {
      field: 'description',
      header: 'Description',
      sortable: true,
      body: (category: Category) => category.description || 'No description',
    },
    {
      field: 'actions',
      header: 'Actions',
      body: categoryActionsBodyTemplate,
      style: { width: '150px' },
    },
  ];

  // Subcategory DataGrid columns configuration
  const subcategoryColumns: ColumnConfig[] = [
    {
      field: 'imagePath',
      header: 'Image',
      body: subcategoryImageBodyTemplate,
      style: { width: '80px' },
    },
    {
      field: 'name',
      header: 'Name',
      sortable: true,
    },
    {
      field: 'description',
      header: 'Description',
      sortable: true,
      body: (subcategory: Subcategory) => subcategory.description || 'No description',
    },
    {
      field: 'categoryName',
      header: 'Parent Category',
      sortable: true,
    },
    {
      field: 'actions',
      header: 'Actions',
      body: subcategoryActionsBodyTemplate,
      style: { width: '150px' },
    },
  ];

  // Product DataGrid columns configuration
  const productColumns: ColumnConfig[] = [
    {
      field: 'imagePaths',
      header: 'Image',
      body: productImageBodyTemplate,
      style: { width: '80px' },
    },
    {
      field: 'name',
      header: 'Name',
      sortable: true,
    },
    {
      field: 'description',
      header: 'Description',
      sortable: true,
      body: (product: Product) => product.description || 'No description',
    },
    {
      field: 'price',
      header: 'Price',
      sortable: true,
      body: (product: Product) => `$${product.price?.toFixed(2) || '0.00'}`,
    },
    {
      field: 'categoryName',
      header: 'Category',
      sortable: true,
    },
    {
      field: 'subcategoryName',
      header: 'Subcategory',
      sortable: true,
    },
    {
      field: 'actions',
      header: 'Actions',
      body: productActionsBodyTemplate,
      style: { width: '150px' },
    },
  ];

  // Render product card skeleton for loading state
  const renderProductCardSkeleton = useCallback((index: number) => {
    return (
      <div className="product-card-skeleton" key={`skeleton-${index}`}>
        <div className="skeleton-image"></div>
        <div className="skeleton-content">
          <div className="skeleton-line title"></div>
          <div className="skeleton-line description"></div>
          <div className="skeleton-line description" style={{ width: '60%' }}></div>
          <div className="skeleton-line price"></div>
          <div className="skeleton-line category"></div>
          <div className="skeleton-line category" style={{ width: '50%' }}></div>
        </div>
      </div>
    );
  }, []);

  // Render product card
  const renderProductCard = useCallback((product: Product) => {
    return (
      <div className="product-card" key={product.id}>
        <div className="product-card-image">
          {product.imagePaths && product.imagePaths.length > 0 ? (
            <img src={product.imagePaths[0]} alt={product.name} />
          ) : (
            <div className="product-image-placeholder">
              <i className="pi pi-image"></i>
            </div>
          )}
        </div>
        <div className="product-card-content">
          <h3 className="product-card-title">{product.name}</h3>
          <p className="product-card-description">{product.description || 'No description'}</p>
          <div className="product-card-price">
            ${product.price?.toFixed(2) || '0.00'}
          </div>
          <div className="product-card-details">
            <div className="product-card-category">
              <span className="label">Category:</span> {product.categoryName || 'None'}
            </div>
            <div className="product-card-subcategory">
              <span className="label">Subcategory:</span> {product.subcategoryName || 'None'}
            </div>
          </div>
          <div className="product-card-actions">
            <Button
              variant="outline"
              size="small"
              leftIcon={<i className="pi pi-pencil"></i>}
              onClick={() => handleEditProduct(product)}
            >
              Edit
            </Button>
            <Button
              variant="danger"
              size="small"
              leftIcon={<i className="pi pi-trash"></i>}
              onClick={() => handleDeleteProduct(product)}
            >
              Delete
            </Button>
          </div>
        </div>
      </div>
    );
  }, [handleEditProduct, handleDeleteProduct]);

  // Get dropdown options for categories
  const categoryOptions: DropdownOption[] = [
    { label: 'All Categories', value: null },
    ...categories.map(category => ({
      label: category.name,
      value: category.id || null
    }))
  ];

  // Get dropdown options for subcategories
  // Filter subcategories based on selected category for better UX
  const filteredSubcategories = selectedCategoryId !== null
    ? subcategories.filter(subcategory => subcategory.categoryId === selectedCategoryId)
    : subcategories;

  // Debug logging
  console.log('Filter state:', {
    selectedCategoryId,
    selectedSubcategoryId,
    productFilter,
    productQueryParams,
    productsCount: products.length,
    subcategoriesCount: subcategories.length,
    filteredSubcategoriesCount: filteredSubcategories.length
  });

  const subcategoryOptions: DropdownOption[] = [
    { label: 'All Subcategories', value: null },
    ...filteredSubcategories.map(subcategory => ({
      label: subcategory.name,
      value: subcategory.id || null
    })),
  ];

  // Add disabled option when no subcategories are available for the selected category
  if (selectedCategoryId !== null && filteredSubcategories.length === 0) {
    subcategoryOptions.push({
      label: 'No subcategories available',
      value: null,
      disabled: true
    });
  }

  return (
    <div className="organization-item-catalog p-4">
      <Toast ref={toast} position="top-right" />

      <Card title="Organization Item Catalog" variant="elevated" className="mb-4">
        <TabView activeIndex={activeTabIndex} onTabChange={handleTabChange}>
          {/* Categories Tab */}
          <TabPanel header="Categories">
            <div className="flex justify-content-end mb-3">
              <Button
                variant="primary"
                size="small"
                onClick={handleAddCategory}
              >
                Add Category
              </Button>
            </div>

            <DataGrid
              value={categories}
              columns={categoryColumns}
              totalRecords={categoriesTotal}
              loading={isCategoriesLoading}
              onPage={handleCategoryPageChange}
              onSort={handleCategorySort}
              rows={categoryPageSize}
              rowsPerPageOptions={[10, 25, 50]}
              showGridLines={true}
              stripedRows={true}
              emptyMessage="No categories found"
            />
          </TabPanel>

          {/* Subcategories Tab */}
          <TabPanel header="Subcategories">
            <div className="flex justify-content-between mb-3 header-container">
              <div className="filter-container">
                <Dropdown
                  value={selectedCategoryId}
                  options={categoryOptions}
                  onChange={handleCategoryFilterChange}
                  placeholder="All Categories"
                  className="category-filter"
                  showClear={false}
                />
              </div>
              <Button
                variant="primary"
                size="small"
                onClick={handleAddSubcategory}
              >
                Add Subcategory
              </Button>
            </div>

            <DataGrid
              value={subcategories}
              columns={subcategoryColumns}
              totalRecords={subcategoriesTotal}
              loading={isSubcategoriesLoading}
              onPage={handleSubcategoryPageChange}
              onSort={handleSubcategorySort}
              rows={subcategoryPageSize}
              rowsPerPageOptions={[10, 25, 50]}
              showGridLines={true}
              stripedRows={true}
              emptyMessage="No subcategories found"
            />
          </TabPanel>

          {/* Products Tab */}
          <TabPanel header="Products">
            <div className="flex justify-content-between mb-3 header-container">
              <div className="filter-container">
                <Dropdown
                  value={selectedCategoryId}
                  options={categoryOptions}
                  onChange={handleProductCategoryFilterChange}
                  placeholder="All Categories"
                  className="category-filter mr-2"
                  showClear={false}
                />
                <Dropdown
                  value={selectedSubcategoryId}
                  options={subcategoryOptions}
                  onChange={handleProductSubcategoryFilterChange}
                  placeholder="All Subcategories"
                  className="subcategory-filter"
                  disabled={false}
                  showClear={false}
                />
              </div>
              <Button
                variant="primary"
                size="small"
                onClick={handleAddProduct}
              >
                Add Product
              </Button>
            </div>

            <div className={isProductsLoading ? "product-cards-loading" : "product-cards-container"}>
              {isProductsLoading ? (
                Array.from({ length: productPageSize }, (_, index) => renderProductCardSkeleton(index))
              ) : products.length > 0 ? (
                products.map(product => renderProductCard(product))
              ) : (
                <div className="empty-message">No products found</div>
              )}
            </div>

            {/* Pagination for products */}
            {productsTotal > productPageSize && (
              <Paginator
                first={(productCurrentPage - 1) * productPageSize}
                rows={productPageSize}
                totalRecords={productsTotal}
                rowsPerPageOptions={[12, 24, 48]}
                onPageChange={handleProductPageChange}
                className="mt-3 product-paginator"
              />
            )}
          </TabPanel>
        </TabView>
      </Card>

      {/* Delete Confirmation Modal */}
      <Modal
        visible={deleteModalVisible}
        onHide={cancelDelete}
        header={`Confirm Delete ${deleteItemType}`}
        footerButtons={[
          {
            label: 'Cancel',
            onClick: cancelDelete,
            variant: 'outline',
          },
          {
            label: 'Delete',
            onClick: confirmDelete,
            variant: 'danger',
            isLoading:
              (deleteItemType === 'category' && deleteCategoryMutation.isPending) ||
              (deleteItemType === 'subcategory' && deleteSubcategoryMutation.isPending) ||
              (deleteItemType === 'product' && deleteProductMutation.isPending),
          },
        ]}
      >
        <p>
          Are you sure you want to delete the {deleteItemType} "{itemToDelete?.name}"?
          This action cannot be undone.
        </p>
      </Modal>
    </div>
  );
};

export default OrganizationItemCatalog;